# VPP数据处理综合报告

## 概述
本报告总结了虚拟电厂(VPP)数据处理的结果，包括储能规格计算和风电数据补充。

## 数据集概况

### 1. CityLearn挑战赛数据集
- **建筑数量**: 17栋
- **储能配置**: 每栋建筑6.4kWh/5.0kW
- **光伏配置**: 4-15kW不等
- **特点**: 储能规格明确，缺少风力数据

### 2. 德州特拉维斯县数据集
- **建筑数量**: ~100栋
- **储能配置**: autosize模式 → 已计算具体规格
- **光伏配置**: 基于屋顶面积自动计算
- **特点**: 包含完整气象数据

### 3. 加州阿拉米达县数据集
- **建筑数量**: ~100栋
- **储能配置**: autosize模式 → 已计算具体规格
- **光伏配置**: 基于屋顶面积自动计算
- **特点**: 包含完整气象数据

### 4. 佛蒙特州奇滕登县数据集
- **建筑数量**: 47栋
- **储能配置**: autosize模式 → 已计算具体规格
- **光伏配置**: 基于屋顶面积自动计算
- **特点**: 提供风力数据源

## 储能规格计算结果

### 计算方法
基于CityLearn数据集的储能配置比例，采用混合方法：
1. **光伏比例法**: 储能容量/光伏功率、储能功率/光伏功率
2. **负荷比例法**: 储能容量/平均负荷、储能功率/峰值负荷
3. **混合方法**: 结合上述两种方法的平均值

### 配置原则
- 储能容量范围: 1-50 kWh
- 储能功率范围: 0.5-25 kW
- 效率: 90%
- 损耗系数: 1e-05

## 风电数据补充结果

### 数据来源
从佛蒙特州奇滕登县的weather.epw文件提取：
- 风速数据（m/s）
- 风向数据（度）
- 其他气象参数

### 风力发电模型
- 风机类型: 10kW小型风机
- 切入风速: 3.0 m/s
- 额定风速: 12.0 m/s
- 切出风速: 25.0 m/s
- 功率系数: 0.35

### 应用结果
为CityLearn数据集创建了包含风力数据的增强天气文件：
- 原始天气数据 + 风速/风向
- 计算得到的风力发电功率
- 保存为weather_with_wind.csv

## 三层级VPP架构配置建议

### 小型VPP (1-3栋建筑)
- **数据源**: CityLearn数据集的Building_1-3组合
- **储能总容量**: 19.2 kWh (3×6.4)
- **储能总功率**: 15.0 kW (3×5.0)
- **光伏总容量**: 20.0 kW
- **风电容量**: 10.0 kW (新增)

### 中型VPP (17栋建筑聚合)
- **数据源**: CityLearn完整数据集
- **储能总容量**: 108.8 kWh (17×6.4)
- **储能总功率**: 85.0 kW (17×5.0)
- **光伏总容量**: 119.0 kW
- **风电容量**: 40.0 kW (新增)

### 大型VPP (~100栋建筑)
- **德州VPP**: 基于tx_travis_county数据
- **加州VPP**: 基于ca_alameda_county数据
- **佛蒙特VPP**: 基于vt_chittenden_county数据
- **储能配置**: 根据计算结果自动配置
- **风电配置**: 基于当地风力资源

## 文件输出清单

### 更新的配置文件
- `dataset/tx_travis_county_neighborhood/schema_with_storage.json`
- `dataset/ca_alameda_county_neighborhood/schema_with_storage.json`
- `dataset/vt_chittenden_county_neighborhood/schema_with_storage.json`

### 增强的数据文件
- `dataset/citylearn_challenge_2022_phase_all_plus_evs/weather_with_wind.csv`

### 分析报告
- `results/storage_specs_texas.txt`
- `results/storage_specs_california.txt`
- `results/storage_specs_vermont.txt`
- `results/wind_resource_analysis.txt`

### 可视化图表
- `results/wind_analysis.png`

## 下一步建议

1. **验证储能规格**: 通过负荷分析验证计算的储能规格是否合理
2. **风电模型优化**: 根据实际风机参数调整风力发电模型
3. **数据集成**: 开发统一的数据接口，整合所有数据集
4. **仿真测试**: 使用处理后的数据进行VPP仿真测试

## 技术说明

### 储能规格计算公式
```
储能容量 = (光伏容量 × 容量比例 + 平均负荷 × 负荷比例) / 2
储能功率 = (光伏功率 × 功率比例 + 峰值负荷 × 负荷比例) / 2
```

### 风力发电功率计算
```
当 v < v_cut_in 或 v > v_cut_out: P = 0
当 v_cut_in ≤ v ≤ v_rated: P = (v/v_rated)³ × Cp × P_rated
当 v > v_rated: P = Cp × P_rated
```

其中：v为风速，Cp为功率系数，P_rated为额定功率
