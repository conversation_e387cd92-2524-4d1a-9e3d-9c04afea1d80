"""
风电资源评估器
评估各个数据集的风力资源潜力，决定是否配置风电
"""

import pandas as pd
import numpy as np
import os
import json
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
from wind_data_processor import WindDataProcessor


class WindResourceEvaluator:
    """风电资源评估器"""
    
    def __init__(self):
        self.datasets = {
            'texas': 'dataset/tx_travis_county_neighborhood',
            'california': 'dataset/ca_alameda_county_neighborhood', 
            'vermont': 'dataset/vt_chittenden_county_neighborhood'
        }
        self.evaluation_results = {}
        self.wind_quality_thresholds = {
            'excellent': {'capacity_factor': 0.25, 'mean_wind_speed': 6.0},
            'good': {'capacity_factor': 0.15, 'mean_wind_speed': 4.5},
            'fair': {'capacity_factor': 0.10, 'mean_wind_speed': 3.5},
            'poor': {'capacity_factor': 0.05, 'mean_wind_speed': 2.5}
        }
    
    def evaluate_all_datasets(self) -> Dict:
        """评估所有数据集的风力资源"""
        print("=== 开始风电资源评估 ===\n")
        
        for dataset_name, dataset_path in self.datasets.items():
            if not os.path.exists(dataset_path):
                print(f"警告: 数据集路径不存在 - {dataset_path}")
                continue
                
            epw_path = os.path.join(dataset_path, "weather.epw")
            if not os.path.exists(epw_path):
                print(f"警告: {dataset_name} 数据集缺少weather.epw文件")
                continue
            
            print(f"评估 {dataset_name} 数据集的风力资源...")
            try:
                processor = WindDataProcessor(dataset_path)
                wind_data = processor.extract_wind_data_from_epw()
                analysis = processor.analyze_wind_resource()
                
                # 评估风力资源等级
                quality_rating = self._rate_wind_quality(analysis)
                
                # 计算经济性指标
                economic_metrics = self._calculate_economic_metrics(analysis)
                
                self.evaluation_results[dataset_name] = {
                    'dataset_path': dataset_path,
                    'wind_analysis': analysis,
                    'quality_rating': quality_rating,
                    'economic_metrics': economic_metrics,
                    'recommendation': self._generate_recommendation(quality_rating, economic_metrics)
                }
                
                print(f"✓ {dataset_name} 评估完成 - 风力资源等级: {quality_rating}")
                
            except Exception as e:
                print(f"✗ {dataset_name} 评估失败: {e}")
                self.evaluation_results[dataset_name] = {
                    'error': str(e),
                    'recommendation': 'no_wind'
                }
        
        return self.evaluation_results
    
    def _rate_wind_quality(self, analysis: Dict) -> str:
        """评估风力资源质量等级"""
        capacity_factor = analysis['wind_power_stats']['capacity_factor']
        mean_wind_speed = analysis['mean_wind_speed']
        
        if (capacity_factor >= self.wind_quality_thresholds['excellent']['capacity_factor'] and 
            mean_wind_speed >= self.wind_quality_thresholds['excellent']['mean_wind_speed']):
            return 'excellent'
        elif (capacity_factor >= self.wind_quality_thresholds['good']['capacity_factor'] and 
              mean_wind_speed >= self.wind_quality_thresholds['good']['mean_wind_speed']):
            return 'good'
        elif (capacity_factor >= self.wind_quality_thresholds['fair']['capacity_factor'] and 
              mean_wind_speed >= self.wind_quality_thresholds['fair']['mean_wind_speed']):
            return 'fair'
        else:
            return 'poor'
    
    def _calculate_economic_metrics(self, analysis: Dict) -> Dict:
        """计算风电项目经济性指标"""
        power_stats = analysis['wind_power_stats']
        
        # 假设参数
        turbine_cost = 15000  # 10kW风机成本 (USD)
        electricity_price = 0.12  # 电价 (USD/kWh)
        project_lifetime = 20  # 项目寿命 (年)
        
        annual_energy = power_stats['total_energy_kwh']
        annual_revenue = annual_energy * electricity_price
        
        # 简单投资回收期
        payback_period = turbine_cost / annual_revenue if annual_revenue > 0 else float('inf')
        
        # 生命周期收益
        lifetime_revenue = annual_revenue * project_lifetime
        net_profit = lifetime_revenue - turbine_cost
        
        return {
            'annual_energy_kwh': annual_energy,
            'annual_revenue_usd': annual_revenue,
            'payback_period_years': payback_period,
            'lifetime_revenue_usd': lifetime_revenue,
            'net_profit_usd': net_profit,
            'roi_percent': (net_profit / turbine_cost * 100) if turbine_cost > 0 else 0
        }
    
    def _generate_recommendation(self, quality_rating: str, economic_metrics: Dict) -> str:
        """生成风电配置建议"""
        payback_period = economic_metrics['payback_period_years']
        roi = economic_metrics['roi_percent']
        
        if quality_rating == 'excellent' and payback_period <= 10 and roi > 100:
            return 'highly_recommended'
        elif quality_rating in ['excellent', 'good'] and payback_period <= 15 and roi > 50:
            return 'recommended'
        elif quality_rating in ['good', 'fair'] and payback_period <= 20 and roi > 0:
            return 'conditional'
        else:
            return 'not_recommended'
    
    def generate_comprehensive_report(self) -> str:
        """生成综合评估报告"""
        report = "# 风电资源综合评估报告\n\n"
        
        report += "## 评估概述\n"
        report += "本报告评估了三个数据集区域的风力资源潜力，为VPP风电配置提供决策依据。\n\n"
        
        report += "## 评估标准\n"
        report += "### 风力资源等级标准\n"
        report += "- **优秀 (Excellent)**: 容量因子 ≥ 25%, 平均风速 ≥ 6.0 m/s\n"
        report += "- **良好 (Good)**: 容量因子 ≥ 15%, 平均风速 ≥ 4.5 m/s\n"
        report += "- **一般 (Fair)**: 容量因子 ≥ 10%, 平均风速 ≥ 3.5 m/s\n"
        report += "- **较差 (Poor)**: 容量因子 < 10%, 平均风速 < 3.5 m/s\n\n"
        
        report += "### 经济性评估标准\n"
        report += "- **强烈推荐**: 投资回收期 ≤ 10年, ROI > 100%\n"
        report += "- **推荐**: 投资回收期 ≤ 15年, ROI > 50%\n"
        report += "- **有条件推荐**: 投资回收期 ≤ 20年, ROI > 0%\n"
        report += "- **不推荐**: 投资回收期 > 20年, ROI ≤ 0%\n\n"
        
        report += "## 各区域评估结果\n\n"
        
        for dataset_name, result in self.evaluation_results.items():
            if 'error' in result:
                report += f"### {dataset_name.title()} 区域\n"
                report += f"**状态**: 评估失败 - {result['error']}\n"
                report += f"**建议**: 不配置风电\n\n"
                continue
            
            analysis = result['wind_analysis']
            quality = result['quality_rating']
            economics = result['economic_metrics']
            recommendation = result['recommendation']
            
            report += f"### {dataset_name.title()} 区域\n"
            
            # 风力资源指标
            report += "#### 风力资源指标\n"
            report += f"- **平均风速**: {analysis['mean_wind_speed']:.2f} m/s\n"
            report += f"- **最大风速**: {analysis['max_wind_speed']:.2f} m/s\n"
            report += f"- **风速标准差**: {analysis['std_wind_speed']:.2f} m/s\n"
            report += f"- **容量因子**: {analysis['wind_power_stats']['capacity_factor']:.1%}\n"
            report += f"- **年发电量**: {analysis['wind_power_stats']['total_energy_kwh']:.0f} kWh\n"
            report += f"- **资源等级**: {quality.upper()}\n\n"
            
            # 经济性指标
            report += "#### 经济性指标\n"
            report += f"- **年发电收益**: ${economics['annual_revenue_usd']:.0f}\n"
            report += f"- **投资回收期**: {economics['payback_period_years']:.1f} 年\n"
            report += f"- **投资回报率**: {economics['roi_percent']:.1f}%\n"
            report += f"- **20年净利润**: ${economics['net_profit_usd']:.0f}\n\n"
            
            # 配置建议
            report += "#### 配置建议\n"
            recommendation_text = {
                'highly_recommended': '**强烈推荐配置风电** - 风力资源优秀，经济效益显著',
                'recommended': '**推荐配置风电** - 风力资源良好，具有经济可行性',
                'conditional': '**有条件推荐** - 风力资源一般，需谨慎评估项目风险',
                'not_recommended': '**不推荐配置风电** - 风力资源较差，经济效益不佳'
            }
            report += recommendation_text.get(recommendation, '未知建议') + "\n\n"
        
        # 总结建议
        report += "## 总结与建议\n\n"
        
        recommended_datasets = []
        not_recommended_datasets = []
        
        for dataset_name, result in self.evaluation_results.items():
            if 'error' in result or result['recommendation'] == 'not_recommended':
                not_recommended_datasets.append(dataset_name)
            elif result['recommendation'] in ['highly_recommended', 'recommended']:
                recommended_datasets.append(dataset_name)
        
        if recommended_datasets:
            report += f"### 推荐配置风电的区域\n"
            for dataset in recommended_datasets:
                report += f"- **{dataset.title()}**: 适合风电开发\n"
            report += "\n"
        
        if not_recommended_datasets:
            report += f"### 不推荐配置风电的区域\n"
            for dataset in not_recommended_datasets:
                report += f"- **{dataset.title()}**: 风力资源不足或经济性较差\n"
            report += "\n"
        
        report += "### VPP配置策略建议\n"
        report += "1. **优先发展**: 在风力资源优秀的区域配置风电，提高VPP的可再生能源比例\n"
        report += "2. **因地制宜**: 根据各区域的资源禀赋，采用不同的能源配置策略\n"
        report += "3. **经济优化**: 重点关注投资回收期和长期收益，确保项目可持续性\n"
        report += "4. **技术选型**: 根据风速分布特征选择合适的风机类型和容量\n\n"
        
        return report
    
    def save_results(self, output_dir: str = "results"):
        """保存评估结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存详细评估数据
        results_file = os.path.join(output_dir, "wind_resource_evaluation.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            # 转换numpy类型为Python原生类型以便JSON序列化
            serializable_results = self._make_json_serializable(self.evaluation_results)
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        # 保存综合报告
        report = self.generate_comprehensive_report()
        report_file = os.path.join(output_dir, "wind_resource_evaluation_report.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"风电资源评估结果已保存到: {output_dir}")
        return results_file, report_file
    
    def _make_json_serializable(self, obj):
        """将numpy类型转换为JSON可序列化的类型"""
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj


def main():
    """主函数"""
    evaluator = WindResourceEvaluator()
    
    # 评估所有数据集
    results = evaluator.evaluate_all_datasets()
    
    # 保存结果
    evaluator.save_results()
    
    # 打印简要结果
    print("\n=== 风电资源评估结果摘要 ===")
    for dataset_name, result in results.items():
        if 'error' in result:
            print(f"{dataset_name}: 评估失败")
        else:
            quality = result['quality_rating']
            recommendation = result['recommendation']
            capacity_factor = result['wind_analysis']['wind_power_stats']['capacity_factor']
            print(f"{dataset_name}: {quality} (容量因子: {capacity_factor:.1%}) - {recommendation}")


if __name__ == "__main__":
    main()
